## Todo List

- [ ] Phase 1: Analyze the reference website and gather requirements
  - [x] Navigate to the reference website
  - [x] Identify key UI elements and their functionalities
  - [ ] Understand the user interaction flow
- [x] Phase 2: Design and develop the website
  - [x] Create the basic HTML structure
  - [x] Apply CSS for styling, including the specified background
  - [x] Implement JavaScript for file selection and drag-and-drop functionality
- [x] Phase 3: Test the website and deliver results
  - [x] Test the website functionality
  - [x] Provide the code to the user



- [x] Identify key UI elements and their functionalities
- [ ] Understand the user interaction flow

