* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    direction: rtl;
    text-align: right;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-content {
    width: 100%;
    max-width: 800px;
    text-align: center;
}

.title {
    font-size: 3.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 0.8s ease-out;
}

.description {
    font-size: 1.2rem;
    color: #5a6c7d;
    margin-bottom: 50px;
    line-height: 1.6;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.upload-section {
    margin-bottom: 60px;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.upload-area {
    background: white;
    border: 3px dashed #e74c3c;
    border-radius: 20px;
    padding: 60px 40px;
    margin-bottom: 30px;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.upload-area:hover {
    border-color: #c0392b;
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.upload-area.dragover {
    border-color: #27ae60;
    background-color: #f8fff8;
    transform: scale(1.02);
}

#fileInput {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-content {
    pointer-events: none;
}

.upload-icon {
    font-size: 4rem;
    color: #e74c3c;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

.select-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    border: none;
    padding: 18px 40px;
    font-size: 1.3rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
    pointer-events: auto;
    margin-bottom: 15px;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.select-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(231, 76, 60, 0.4);
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
}

.select-btn:active {
    transform: translateY(-1px);
}

.drag-text {
    color: #7f8c8d;
    font-size: 1.1rem;
    margin-top: 10px;
}

.cloud-options {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.cloud-btn {
    background: white;
    border: 2px solid #ecf0f1;
    padding: 15px 25px;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
    font-weight: 500;
    color: #2c3e50;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.cloud-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.google-drive:hover {
    border-color: #4285f4;
    color: #4285f4;
}

.dropbox:hover {
    border-color: #0061ff;
    color: #0061ff;
}

.cloud-btn i {
    font-size: 1.2rem;
}

.features-section {
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

.features-section h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 40px;
    font-weight: 600;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-card {
    background: white;
    padding: 30px 25px;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #ecf0f1;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: #e74c3c;
}

.feature-card i {
    font-size: 2.5rem;
    color: #e74c3c;
    margin-bottom: 20px;
}

.feature-card h3 {
    font-size: 1.4rem;
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 600;
}

.feature-card p {
    color: #7f8c8d;
    line-height: 1.6;
    font-size: 1rem;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.loading-spinner i {
    font-size: 3rem;
    color: #e74c3c;
    margin-bottom: 20px;
}

.loading-spinner p {
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 500;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 20px 15px;
    }
    
    .title {
        font-size: 2.5rem;
    }
    
    .description {
        font-size: 1.1rem;
        margin-bottom: 40px;
    }
    
    .upload-area {
        padding: 40px 20px;
    }
    
    .select-btn {
        padding: 15px 30px;
        font-size: 1.1rem;
    }
    
    .cloud-options {
        flex-direction: column;
        align-items: center;
    }
    
    .cloud-btn {
        width: 200px;
        justify-content: center;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .features-section h2 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 2rem;
    }
    
    .upload-area {
        padding: 30px 15px;
    }
    
    .upload-icon {
        font-size: 3rem;
    }
    
    .select-btn {
        padding: 12px 25px;
        font-size: 1rem;
    }
}

