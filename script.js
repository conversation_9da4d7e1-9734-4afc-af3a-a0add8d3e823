document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const selectBtn = document.getElementById('selectBtn');
    const googleDriveBtn = document.getElementById('googleDriveBtn');
    const dropboxBtn = document.getElementById('dropboxBtn');
    const loadingOverlay = document.getElementById('loadingOverlay');

    // File input change handler
    fileInput.addEventListener('change', handleFileSelect);

    // Click handlers
    selectBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        fileInput.click();
    });

    uploadArea.addEventListener('click', function(e) {
        // Only trigger file input if clicking on the upload area itself, not on buttons
        if (e.target === uploadArea || e.target.closest('.upload-content')) {
            fileInput.click();
        }
    });
    
    googleDriveBtn.addEventListener('click', function() {
        showNotification('ميزة Google Drive ستكون متاحة قريباً', 'info');
    });
    
    dropboxBtn.addEventListener('click', function() {
        showNotification('ميزة Dropbox ستكون متاحة قريباً', 'info');
    });

    // Drag and drop functionality
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFiles(files);
        }
    });

    // Handle file selection
    function handleFileSelect(e) {
        const files = e.target.files;
        if (files.length > 0) {
            handleFiles(files);
            // Clear the input to allow selecting the same file again
            e.target.value = '';
        }
    }

    // Process selected files
    function handleFiles(files) {
        const validFiles = [];

        for (let file of files) {
            if (file.type === 'application/pdf') {
                validFiles.push(file);
            } else {
                showNotification(`الملف ${file.name} ليس ملف PDF صالح`, 'error');
            }
        }

        if (validFiles.length > 0) {
            processFiles(validFiles);
        }
    }

    // Process valid PDF files
    function processFiles(files) {
        showLoading(true);

        // Validate file sizes (max 10MB per file)
        const maxSize = 10 * 1024 * 1024; // 10MB
        const validSizeFiles = [];

        for (let file of files) {
            if (file.size > maxSize) {
                showNotification(`الملف ${file.name} كبير جداً. الحد الأقصى 10 ميجابايت`, 'error');
            } else {
                validSizeFiles.push(file);
            }
        }

        if (validSizeFiles.length === 0) {
            showLoading(false);
            return;
        }

        // Simulate file processing with realistic timing
        setTimeout(() => {
            showLoading(false);

            if (validSizeFiles.length === 1) {
                showNotification(`تم تحميل الملف: ${validSizeFiles[0].name} بنجاح!`, 'success');
                showEditor(validSizeFiles[0]);
            } else {
                showNotification(`تم تحميل ${validSizeFiles.length} ملفات بنجاح!`, 'success');
                showMultipleFiles(validSizeFiles);
            }
        }, 1500);
    }

    // Show/hide loading overlay
    function showLoading(show) {
        loadingOverlay.style.display = show ? 'flex' : 'none';
    }

    // Show notification
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        `;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: getNotificationColor(type),
            color: 'white',
            padding: '15px 20px',
            borderRadius: '10px',
            boxShadow: '0 5px 15px rgba(0, 0, 0, 0.2)',
            zIndex: '1001',
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            fontSize: '1rem',
            fontWeight: '500',
            minWidth: '300px',
            animation: 'slideInRight 0.3s ease-out'
        });
        
        document.body.appendChild(notification);
        
        // Remove notification after 4 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 4000);
    }

    // Get notification icon based on type
    function getNotificationIcon(type) {
        switch (type) {
            case 'success': return 'fa-check-circle';
            case 'error': return 'fa-exclamation-circle';
            case 'warning': return 'fa-exclamation-triangle';
            default: return 'fa-info-circle';
        }
    }

    // Get notification color based on type
    function getNotificationColor(type) {
        switch (type) {
            case 'success': return '#27ae60';
            case 'error': return '#e74c3c';
            case 'warning': return '#f39c12';
            default: return '#3498db';
        }
    }

    // Show editor interface similar to ilovepdf
    function showEditor(file) {
        // Hide the main content
        document.querySelector('.main-content').style.display = 'none';

        // Create editor interface
        const editorContainer = document.createElement('div');
        editorContainer.className = 'editor-container';
        editorContainer.innerHTML = `
            <div class="editor-header">
                <div class="header-left">
                    <button class="back-btn" id="backBtn">
                        <i class="fas fa-arrow-right"></i>
                        العودة
                    </button>
                    <div class="file-info">
                        <i class="fas fa-file-pdf"></i>
                        <span class="file-name">${file.name}</span>
                        <span class="file-size">(${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                    </div>
                </div>
                <div class="header-right">
                    <button class="download-btn" id="downloadBtn">
                        <i class="fas fa-download"></i>
                        تحميل PDF
                    </button>
                </div>
            </div>

            <div class="editor-main">
                <div class="editor-sidebar">
                    <div class="tool-section">
                        <h3>أدوات التحرير</h3>
                        <div class="tool-group">
                            <button class="tool-btn active" data-tool="text">
                                <i class="fas fa-font"></i>
                                <span>إضافة نص</span>
                            </button>
                            <button class="tool-btn" data-tool="highlight">
                                <i class="fas fa-highlighter"></i>
                                <span>تمييز النص</span>
                            </button>
                            <button class="tool-btn" data-tool="shape">
                                <i class="fas fa-shapes"></i>
                                <span>الأشكال</span>
                            </button>
                            <button class="tool-btn" data-tool="comment">
                                <i class="fas fa-comment"></i>
                                <span>تعليق</span>
                            </button>
                            <button class="tool-btn" data-tool="signature">
                                <i class="fas fa-signature"></i>
                                <span>التوقيع</span>
                            </button>
                            <button class="tool-btn" data-tool="image">
                                <i class="fas fa-image"></i>
                                <span>صورة</span>
                            </button>
                        </div>
                    </div>

                    <div class="tool-options" id="toolOptions">
                        <div class="text-options tool-option active" data-tool="text">
                            <h4>خيارات النص</h4>
                            <div class="option-group">
                                <label>حجم الخط:</label>
                                <select id="fontSize">
                                    <option value="12">12</option>
                                    <option value="14" selected>14</option>
                                    <option value="16">16</option>
                                    <option value="18">18</option>
                                    <option value="20">20</option>
                                    <option value="24">24</option>
                                </select>
                            </div>
                            <div class="option-group">
                                <label>لون النص:</label>
                                <input type="color" id="textColor" value="#000000">
                            </div>
                            <div class="option-group">
                                <label>نوع الخط:</label>
                                <select id="fontFamily">
                                    <option value="Arial">Arial</option>
                                    <option value="Times New Roman">Times New Roman</option>
                                    <option value="Helvetica">Helvetica</option>
                                </select>
                            </div>
                        </div>

                        <div class="highlight-options tool-option" data-tool="highlight">
                            <h4>خيارات التمييز</h4>
                            <div class="color-palette">
                                <div class="color-option" data-color="#ffff00" style="background: #ffff00;"></div>
                                <div class="color-option" data-color="#00ff00" style="background: #00ff00;"></div>
                                <div class="color-option" data-color="#ff00ff" style="background: #ff00ff;"></div>
                                <div class="color-option" data-color="#00ffff" style="background: #00ffff;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="editor-workspace">
                    <div class="pdf-viewer" id="pdfViewer">
                        <div class="pdf-page">
                            <div class="page-content">
                                <div class="pdf-placeholder">
                                    <i class="fas fa-file-pdf"></i>
                                    <h3>معاينة PDF</h3>
                                    <p>سيتم عرض محتوى الملف هنا</p>
                                    <p class="file-details">الملف: ${file.name}</p>
                                    <p class="file-details">الحجم: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add to container
        document.querySelector('.container').appendChild(editorContainer);

        // Add event listeners
        document.getElementById('backBtn').addEventListener('click', function() {
            editorContainer.remove();
            document.querySelector('.main-content').style.display = 'block';
        });

        document.getElementById('downloadBtn').addEventListener('click', function() {
            downloadEditedPDF(file);
        });

        // Add save functionality with Ctrl+S
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                downloadEditedPDF(file);
            }
        });

        // Tool selection
        const toolBtns = document.querySelectorAll('.tool-btn');
        const toolOptions = document.querySelectorAll('.tool-option');

        toolBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const tool = this.dataset.tool;

                // Update active tool
                toolBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Show corresponding options
                toolOptions.forEach(option => {
                    option.classList.remove('active');
                    if (option.dataset.tool === tool) {
                        option.classList.add('active');
                    }
                });

                showNotification(`تم تحديد أداة: ${this.querySelector('span').textContent}`, 'info');
            });
        });

        // Color palette for highlighting
        const colorOptions = document.querySelectorAll('.color-option');
        colorOptions.forEach(option => {
            option.addEventListener('click', function() {
                colorOptions.forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                showNotification(`تم تحديد اللون للتمييز`, 'info');
            });
        });

        // Add interactive functionality to PDF viewer
        const pdfViewer = document.getElementById('pdfViewer');
        let currentTool = 'text';

        // Update current tool when tool buttons are clicked
        toolBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                currentTool = this.dataset.tool;
            });
        });

        // Add click functionality to PDF page
        const pdfPage = pdfViewer.querySelector('.pdf-page');
        pdfPage.addEventListener('click', function(e) {
            if (currentTool === 'text') {
                addTextElement(e);
            } else if (currentTool === 'comment') {
                addCommentElement(e);
            }
        });

        // Function to add text element
        function addTextElement(e) {
            const rect = pdfPage.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const textElement = document.createElement('div');
            textElement.className = 'text-element';
            textElement.contentEditable = true;
            textElement.textContent = 'اكتب النص هنا';
            textElement.style.cssText = `
                position: absolute;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.9);
                border: 2px dashed #e74c3c;
                padding: 5px 10px;
                border-radius: 4px;
                font-size: ${document.getElementById('fontSize').value}px;
                color: ${document.getElementById('textColor').value};
                font-family: ${document.getElementById('fontFamily').value};
                min-width: 100px;
                cursor: text;
                z-index: 10;
            `;

            pdfPage.appendChild(textElement);
            textElement.focus();
            textElement.select();

            // Add delete functionality
            textElement.addEventListener('dblclick', function() {
                if (confirm('هل تريد حذف هذا النص؟')) {
                    this.remove();
                }
            });

            showNotification('تم إضافة نص جديد. انقر مرتين للحذف', 'success');
        }

        // Function to add comment element
        function addCommentElement(e) {
            const rect = pdfPage.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const commentElement = document.createElement('div');
            commentElement.className = 'comment-element';
            commentElement.innerHTML = `
                <div class="comment-icon">
                    <i class="fas fa-comment"></i>
                </div>
                <div class="comment-popup">
                    <textarea placeholder="اكتب تعليقك هنا..."></textarea>
                    <div class="comment-actions">
                        <button class="save-comment">حفظ</button>
                        <button class="cancel-comment">إلغاء</button>
                    </div>
                </div>
            `;
            commentElement.style.cssText = `
                position: absolute;
                left: ${x}px;
                top: ${y}px;
                z-index: 10;
            `;

            pdfPage.appendChild(commentElement);

            // Comment functionality
            const popup = commentElement.querySelector('.comment-popup');
            const saveBtn = commentElement.querySelector('.save-comment');
            const cancelBtn = commentElement.querySelector('.cancel-comment');
            const textarea = commentElement.querySelector('textarea');

            saveBtn.addEventListener('click', function() {
                if (textarea.value.trim()) {
                    popup.style.display = 'none';
                    commentElement.title = textarea.value;
                    showNotification('تم حفظ التعليق', 'success');
                } else {
                    showNotification('يرجى كتابة تعليق', 'warning');
                }
            });

            cancelBtn.addEventListener('click', function() {
                commentElement.remove();
            });

            // Show popup on icon click
            const icon = commentElement.querySelector('.comment-icon');
            icon.addEventListener('click', function() {
                popup.style.display = popup.style.display === 'none' ? 'block' : 'none';
            });

            textarea.focus();
        }

        // Add styles for new editor interface
        if (!document.getElementById('editorStyles')) {
            const editorStyles = document.createElement('style');
            editorStyles.id = 'editorStyles';
            editorStyles.textContent = `
                .editor-container {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: #f8f9fa;
                    z-index: 1000;
                    display: flex;
                    flex-direction: column;
                }

                .editor-header {
                    background: white;
                    border-bottom: 1px solid #e9ecef;
                    padding: 15px 30px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }

                .header-left {
                    display: flex;
                    align-items: center;
                    gap: 20px;
                }

                .back-btn {
                    background: #e74c3c;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 8px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-weight: 500;
                    transition: all 0.3s ease;
                }

                .back-btn:hover {
                    background: #c0392b;
                    transform: translateY(-2px);
                }

                .file-info {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    color: #2c3e50;
                }

                .file-info i {
                    color: #e74c3c;
                    font-size: 1.2rem;
                }

                .file-name {
                    font-weight: 600;
                    font-size: 1.1rem;
                }

                .file-size {
                    color: #7f8c8d;
                    font-size: 0.9rem;
                }

                .download-btn {
                    background: #27ae60;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 8px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-weight: 500;
                    transition: all 0.3s ease;
                }

                .download-btn:hover {
                    background: #229954;
                    transform: translateY(-2px);
                }

                .editor-main {
                    flex: 1;
                    display: flex;
                    height: calc(100vh - 80px);
                }

                .editor-sidebar {
                    width: 300px;
                    background: white;
                    border-right: 1px solid #e9ecef;
                    padding: 20px;
                    overflow-y: auto;
                }

                .tool-section h3 {
                    color: #2c3e50;
                    margin-bottom: 15px;
                    font-size: 1.2rem;
                    font-weight: 600;
                }

                .tool-group {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                    margin-bottom: 30px;
                }

                .tool-btn {
                    background: #f8f9fa;
                    border: 2px solid #e9ecef;
                    padding: 15px;
                    border-radius: 10px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    text-align: right;
                    color: #2c3e50;
                    font-weight: 500;
                }

                .tool-btn:hover {
                    border-color: #e74c3c;
                    background: #fff5f5;
                    transform: translateY(-2px);
                }

                .tool-btn.active {
                    background: #e74c3c;
                    color: white;
                    border-color: #e74c3c;
                }

                .tool-btn i {
                    font-size: 1.2rem;
                    width: 20px;
                    text-align: center;
                }

                .tool-options {
                    border-top: 1px solid #e9ecef;
                    padding-top: 20px;
                }

                .tool-option {
                    display: none;
                }

                .tool-option.active {
                    display: block;
                }

                .tool-option h4 {
                    color: #2c3e50;
                    margin-bottom: 15px;
                    font-size: 1.1rem;
                }

                .option-group {
                    margin-bottom: 15px;
                }

                .option-group label {
                    display: block;
                    margin-bottom: 5px;
                    color: #2c3e50;
                    font-weight: 500;
                }

                .option-group select,
                .option-group input {
                    width: 100%;
                    padding: 8px 12px;
                    border: 1px solid #e9ecef;
                    border-radius: 6px;
                    font-size: 14px;
                }

                .color-palette {
                    display: grid;
                    grid-template-columns: repeat(4, 1fr);
                    gap: 10px;
                    margin-top: 10px;
                }

                .color-option {
                    width: 40px;
                    height: 40px;
                    border-radius: 8px;
                    cursor: pointer;
                    border: 3px solid transparent;
                    transition: all 0.3s ease;
                }

                .color-option:hover {
                    transform: scale(1.1);
                }

                .color-option.selected {
                    border-color: #2c3e50;
                }

                .editor-workspace {
                    flex: 1;
                    padding: 20px;
                    overflow: auto;
                    background: #f1f3f4;
                }

                .pdf-viewer {
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 10px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                    overflow: hidden;
                }

                .pdf-page {
                    min-height: 600px;
                    background: white;
                    position: relative;
                }

                .page-content {
                    padding: 40px;
                    min-height: 600px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .pdf-placeholder {
                    text-align: center;
                    color: #7f8c8d;
                }

                .pdf-placeholder i {
                    font-size: 4rem;
                    color: #e74c3c;
                    margin-bottom: 20px;
                }

                .pdf-placeholder h3 {
                    color: #2c3e50;
                    margin-bottom: 10px;
                    font-size: 1.5rem;
                }

                .pdf-placeholder p {
                    margin-bottom: 8px;
                }

                .file-details {
                    font-size: 0.9rem;
                    color: #95a5a6;
                }

                .text-element {
                    transition: all 0.3s ease;
                }

                .text-element:hover {
                    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                }

                .text-element:focus {
                    border-color: #27ae60;
                    outline: none;
                }

                .comment-element {
                    position: relative;
                }

                .comment-icon {
                    width: 30px;
                    height: 30px;
                    background: #f39c12;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

                .comment-icon:hover {
                    background: #e67e22;
                    transform: scale(1.1);
                }

                .comment-popup {
                    position: absolute;
                    top: 35px;
                    left: 0;
                    background: white;
                    border: 1px solid #e9ecef;
                    border-radius: 8px;
                    padding: 15px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    min-width: 250px;
                    z-index: 20;
                }

                .comment-popup textarea {
                    width: 100%;
                    height: 80px;
                    border: 1px solid #e9ecef;
                    border-radius: 4px;
                    padding: 8px;
                    resize: vertical;
                    font-family: inherit;
                    margin-bottom: 10px;
                }

                .comment-actions {
                    display: flex;
                    gap: 10px;
                    justify-content: flex-end;
                }

                .comment-actions button {
                    padding: 6px 12px;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 0.9rem;
                    transition: all 0.3s ease;
                }

                .save-comment {
                    background: #27ae60;
                    color: white;
                }

                .save-comment:hover {
                    background: #229954;
                }

                .cancel-comment {
                    background: #e74c3c;
                    color: white;
                }

                .cancel-comment:hover {
                    background: #c0392b;
                }

                @media (max-width: 768px) {
                    .editor-sidebar {
                        width: 250px;
                    }

                    .editor-header {
                        padding: 10px 15px;
                        flex-direction: column;
                        gap: 10px;
                    }

                    .header-left {
                        flex-direction: column;
                        gap: 10px;
                    }

                    .comment-popup {
                        min-width: 200px;
                    }
                }
            `;
            document.head.appendChild(editorStyles);
        }

        // Function to download edited PDF
        function downloadEditedPDF(file) {
            showLoading(true);

            // Simulate PDF generation with edits
            setTimeout(() => {
                showLoading(false);

                // Create a download link (simulation)
                const link = document.createElement('a');
                link.href = URL.createObjectURL(file);
                link.download = `edited_${file.name}`;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showNotification('تم تحميل الملف المحرر بنجاح!', 'success');
            }, 2000);
        }
    }

    // Show multiple files interface
    function showMultipleFiles(files) {
        // Create file selection interface
        const fileSelectionModal = document.createElement('div');
        fileSelectionModal.className = 'file-selection-modal';
        fileSelectionModal.innerHTML = `
            <div class="file-selection-content">
                <div class="selection-header">
                    <h3>اختر ملف للتحرير</h3>
                    <button class="close-selection-btn" onclick="this.parentElement.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="files-grid">
                    ${files.map((file, index) => `
                        <div class="file-card" data-file-index="${index}">
                            <div class="file-icon">
                                <i class="fas fa-file-pdf"></i>
                            </div>
                            <div class="file-details">
                                <h4>${file.name}</h4>
                                <p>${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                            </div>
                            <button class="edit-file-btn" data-file-index="${index}">
                                <i class="fas fa-edit"></i>
                                تحرير
                            </button>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        // Style the modal
        Object.assign(fileSelectionModal.style, {
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            background: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: '1002'
        });

        document.body.appendChild(fileSelectionModal);

        // Add event listeners for file selection
        const editBtns = fileSelectionModal.querySelectorAll('.edit-file-btn');
        editBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const fileIndex = parseInt(this.dataset.fileIndex);
                const selectedFile = files[fileIndex];
                fileSelectionModal.remove();
                showEditor(selectedFile);
            });
        });

        // Add styles for file selection modal
        if (!document.getElementById('fileSelectionStyles')) {
            const selectionStyles = document.createElement('style');
            selectionStyles.id = 'fileSelectionStyles';
            selectionStyles.textContent = `
                .file-selection-content {
                    background: white;
                    width: 90%;
                    max-width: 800px;
                    max-height: 80%;
                    border-radius: 15px;
                    overflow: hidden;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                }

                .selection-header {
                    background: #2c3e50;
                    color: white;
                    padding: 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .close-selection-btn {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 1.5rem;
                    cursor: pointer;
                    padding: 5px;
                    border-radius: 5px;
                    transition: background 0.3s ease;
                }

                .close-selection-btn:hover {
                    background: rgba(255, 255, 255, 0.1);
                }

                .files-grid {
                    padding: 30px;
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                    gap: 20px;
                    max-height: 400px;
                    overflow-y: auto;
                }

                .file-card {
                    background: #f8f9fa;
                    border: 2px solid #e9ecef;
                    border-radius: 12px;
                    padding: 20px;
                    text-align: center;
                    transition: all 0.3s ease;
                    cursor: pointer;
                }

                .file-card:hover {
                    border-color: #e74c3c;
                    transform: translateY(-5px);
                    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                }

                .file-icon i {
                    font-size: 3rem;
                    color: #e74c3c;
                    margin-bottom: 15px;
                }

                .file-details h4 {
                    color: #2c3e50;
                    margin-bottom: 5px;
                    font-size: 1rem;
                    word-break: break-word;
                }

                .file-details p {
                    color: #7f8c8d;
                    font-size: 0.9rem;
                    margin-bottom: 15px;
                }

                .edit-file-btn {
                    background: #e74c3c;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 8px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin: 0 auto;
                    font-weight: 500;
                    transition: all 0.3s ease;
                }

                .edit-file-btn:hover {
                    background: #c0392b;
                    transform: translateY(-2px);
                }
            `;
            document.head.appendChild(selectionStyles);
        }
    }

    // Add animation styles
    const animationStyles = document.createElement('style');
    animationStyles.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(animationStyles);

    // Add smooth scrolling for better UX
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + O to open file
        if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
            e.preventDefault();
            fileInput.click();
        }
        
        // Escape to close modals and editor
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.editor-modal, .file-selection-modal');
            modals.forEach(modal => modal.remove());

            const editorContainer = document.querySelector('.editor-container');
            if (editorContainer) {
                editorContainer.remove();
                document.querySelector('.main-content').style.display = 'block';
            }
        }
    });

    // Add feature card interactions
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('click', function() {
            const feature = this.querySelector('h3').textContent;
            showNotification(`ميزة "${feature}" ستكون متاحة في المحرر`, 'info');
        });
    });

    // Initialize tooltips for better UX
    function initTooltips() {
        const tooltipElements = document.querySelectorAll('[title]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', function() {
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip';
                tooltip.textContent = this.getAttribute('title');
                tooltip.style.cssText = `
                    position: absolute;
                    background: #2c3e50;
                    color: white;
                    padding: 8px 12px;
                    border-radius: 6px;
                    font-size: 0.9rem;
                    z-index: 1003;
                    pointer-events: none;
                    white-space: nowrap;
                `;
                document.body.appendChild(tooltip);
                
                const rect = this.getBoundingClientRect();
                tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
            });
            
            element.addEventListener('mouseleave', function() {
                const tooltip = document.querySelector('.tooltip');
                if (tooltip) {
                    tooltip.remove();
                }
            });
        });
    }

    initTooltips();
});

