document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const selectBtn = document.getElementById('selectBtn');
    const googleDriveBtn = document.getElementById('googleDriveBtn');
    const dropboxBtn = document.getElementById('dropboxBtn');
    const loadingOverlay = document.getElementById('loadingOverlay');

    // File input change handler
    fileInput.addEventListener('change', handleFileSelect);
    
    // Click handlers
    selectBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        fileInput.click();
    });
    
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });
    
    googleDriveBtn.addEventListener('click', function() {
        showNotification('ميزة Google Drive ستكون متاحة قريباً', 'info');
    });
    
    dropboxBtn.addEventListener('click', function() {
        showNotification('ميزة Dropbox ستكون متاحة قريباً', 'info');
    });

    // Drag and drop functionality
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFiles(files);
        }
    });

    // Handle file selection
    function handleFileSelect(e) {
        const files = e.target.files;
        if (files.length > 0) {
            handleFiles(files);
        }
    }

    // Process selected files
    function handleFiles(files) {
        const validFiles = [];
        
        for (let file of files) {
            if (file.type === 'application/pdf') {
                validFiles.push(file);
            } else {
                showNotification(`الملف ${file.name} ليس ملف PDF صالح`, 'error');
            }
        }
        
        if (validFiles.length > 0) {
            processFiles(validFiles);
        }
    }

    // Process valid PDF files
    function processFiles(files) {
        showLoading(true);
        
        // Simulate file processing
        setTimeout(() => {
            showLoading(false);
            
            if (files.length === 1) {
                showNotification(`تم تحميل الملف: ${files[0].name} بنجاح!`, 'success');
                showEditor(files[0]);
            } else {
                showNotification(`تم تحميل ${files.length} ملفات بنجاح!`, 'success');
                showMultipleFiles(files);
            }
        }, 2000);
    }

    // Show/hide loading overlay
    function showLoading(show) {
        loadingOverlay.style.display = show ? 'flex' : 'none';
    }

    // Show notification
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        `;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: getNotificationColor(type),
            color: 'white',
            padding: '15px 20px',
            borderRadius: '10px',
            boxShadow: '0 5px 15px rgba(0, 0, 0, 0.2)',
            zIndex: '1001',
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            fontSize: '1rem',
            fontWeight: '500',
            minWidth: '300px',
            animation: 'slideInRight 0.3s ease-out'
        });
        
        document.body.appendChild(notification);
        
        // Remove notification after 4 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 4000);
    }

    // Get notification icon based on type
    function getNotificationIcon(type) {
        switch (type) {
            case 'success': return 'fa-check-circle';
            case 'error': return 'fa-exclamation-circle';
            case 'warning': return 'fa-exclamation-triangle';
            default: return 'fa-info-circle';
        }
    }

    // Get notification color based on type
    function getNotificationColor(type) {
        switch (type) {
            case 'success': return '#27ae60';
            case 'error': return '#e74c3c';
            case 'warning': return '#f39c12';
            default: return '#3498db';
        }
    }

    // Show editor interface (placeholder)
    function showEditor(file) {
        const editorModal = document.createElement('div');
        editorModal.className = 'editor-modal';
        editorModal.innerHTML = `
            <div class="editor-content">
                <div class="editor-header">
                    <h3>محرر PDF - ${file.name}</h3>
                    <button class="close-btn" onclick="this.parentElement.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="editor-body">
                    <div class="editor-toolbar">
                        <button class="tool-btn"><i class="fas fa-font"></i> نص</button>
                        <button class="tool-btn"><i class="fas fa-shapes"></i> أشكال</button>
                        <button class="tool-btn"><i class="fas fa-highlighter"></i> تمييز</button>
                        <button class="tool-btn"><i class="fas fa-comment"></i> تعليق</button>
                    </div>
                    <div class="editor-canvas">
                        <div class="pdf-preview">
                            <i class="fas fa-file-pdf" style="font-size: 5rem; color: #e74c3c; margin: 50px;"></i>
                            <p>معاينة PDF ستظهر هنا</p>
                            <p style="color: #7f8c8d; margin-top: 10px;">الحجم: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Style the modal
        Object.assign(editorModal.style, {
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            background: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: '1002'
        });
        
        document.body.appendChild(editorModal);
        
        // Add styles for editor components
        const editorStyles = document.createElement('style');
        editorStyles.textContent = `
            .editor-content {
                background: white;
                width: 90%;
                max-width: 1200px;
                height: 80%;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            }
            .editor-header {
                background: #2c3e50;
                color: white;
                padding: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .close-btn {
                background: none;
                border: none;
                color: white;
                font-size: 1.5rem;
                cursor: pointer;
                padding: 5px;
                border-radius: 5px;
                transition: background 0.3s ease;
            }
            .close-btn:hover {
                background: rgba(255, 255, 255, 0.1);
            }
            .editor-body {
                height: calc(100% - 80px);
                display: flex;
                flex-direction: column;
            }
            .editor-toolbar {
                background: #ecf0f1;
                padding: 15px;
                display: flex;
                gap: 10px;
                border-bottom: 1px solid #bdc3c7;
            }
            .tool-btn {
                background: white;
                border: 1px solid #bdc3c7;
                padding: 10px 15px;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            .tool-btn:hover {
                background: #e74c3c;
                color: white;
                border-color: #e74c3c;
            }
            .editor-canvas {
                flex: 1;
                background: #f8f9fa;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .pdf-preview {
                text-align: center;
                color: #2c3e50;
            }
        `;
        document.head.appendChild(editorStyles);
    }

    // Show multiple files interface
    function showMultipleFiles(files) {
        const fileList = files.map(file => `
            <div class="file-item">
                <i class="fas fa-file-pdf"></i>
                <span>${file.name}</span>
                <span class="file-size">${(file.size / 1024 / 1024).toFixed(2)} MB</span>
            </div>
        `).join('');
        
        showNotification(`تم تحديد ${files.length} ملفات للتحرير`, 'info');
    }

    // Add animation styles
    const animationStyles = document.createElement('style');
    animationStyles.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(animationStyles);

    // Add smooth scrolling for better UX
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + O to open file
        if ((e.ctrlKey || e.metaKey) && e.key === 'o') {
            e.preventDefault();
            fileInput.click();
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.editor-modal');
            modals.forEach(modal => modal.remove());
        }
    });

    // Add feature card interactions
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('click', function() {
            const feature = this.querySelector('h3').textContent;
            showNotification(`ميزة "${feature}" ستكون متاحة في المحرر`, 'info');
        });
    });

    // Initialize tooltips for better UX
    function initTooltips() {
        const tooltipElements = document.querySelectorAll('[title]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', function() {
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip';
                tooltip.textContent = this.getAttribute('title');
                tooltip.style.cssText = `
                    position: absolute;
                    background: #2c3e50;
                    color: white;
                    padding: 8px 12px;
                    border-radius: 6px;
                    font-size: 0.9rem;
                    z-index: 1003;
                    pointer-events: none;
                    white-space: nowrap;
                `;
                document.body.appendChild(tooltip);
                
                const rect = this.getBoundingClientRect();
                tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
            });
            
            element.addEventListener('mouseleave', function() {
                const tooltip = document.querySelector('.tooltip');
                if (tooltip) {
                    tooltip.remove();
                }
            });
        });
    }

    initTooltips();
});

